@extends('layouts.index')

@section('content')
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title text-center fw-bold py-2">Potansiyel Müşteri Detayı</h3>
                </div>
                <div class="card-body">
                    
                    <!-- Firma Bilgileri -->
                    <h5 class="mb-3 text-primary">Firma Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Firma Adı:</label>
                                <p class="mb-2">{{ $potentialCustomer->company_name ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Yet<PERSON><PERSON>ilgileri -->
                    <h5 class="mb-3 text-primary">Yetki<PERSON>şi Bilgileri</h5>
                    @if($potentialCustomer->authorizedPersons && $potentialCustomer->authorizedPersons->count() > 0)
                        @foreach($potentialCustomer->authorizedPersons as $index => $person)
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-secondary mb-3">Yetkili Kişi #{{ $index + 1 }}</h6>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Ünvan:</label>
                                        <p class="mb-2">{{ $person->title ?? 'Belirtilmemiş' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Telefon:</label>
                                        <p class="mb-2">{{ $person->phone ?? 'Belirtilmemiş' }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili İsim:</label>
                                        <p class="mb-2">{{ $person->name ?? 'Belirtilmemiş' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Soyisim:</label>
                                        <p class="mb-2">{{ $person->lastname ?? 'Belirtilmemiş' }}</p>
                                    </div>
                                </div>
                            </div>
                            @if(!$loop->last)
                                <hr class="my-4">
                            @endif
                        @endforeach
                    @else
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="info-group">
                                    <p class="mb-2 text-muted">Yetkili kişi bilgisi bulunmamaktadır.</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- İletişim Bilgileri -->
                    <h5 class="mb-3 text-primary">İletişim Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şirket Telefon 1:</label>
                                <p class="mb-2">{{ $potentialCustomer->phone_1 ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şirket Telefon 2:</label>
                                <p class="mb-2">{{ $potentialCustomer->phone_2 ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şirket Telefon 3:</label>
                                <p class="mb-2">{{ $potentialCustomer->phone_3 ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Adres Bilgileri -->
                    <h5 class="mb-3 text-primary">Adres Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İl:</label>
                                <p class="mb-2">{{ $potentialCustomer->city ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İlçe:</label>
                                <p class="mb-2">{{ $potentialCustomer->district ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Adres:</label>
                                <p class="mb-2">{{ $potentialCustomer->address ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Teklif ve Durum Bilgileri -->
                    <h5 class="mb-3 text-primary">Teklif ve Durum Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Teklif Durumu:</label>
                                <p class="mb-2">
                                    @switch($potentialCustomer->offer_status)
                                        @case('new')
                                            <span class="badge bg-primary">Yeni</span>
                                            @break
                                        @case('pending')
                                            <span class="badge bg-warning">Beklemede</span>
                                            @break
                                        @case('accepted')
                                            <span class="badge bg-success">Kabul Edildi</span>
                                            @break
                                        @case('rejected')
                                            <span class="badge bg-danger">Reddedildi</span>
                                            @break
                                        @case('customer')
                                            <span class="badge bg-info">Müşteri Oldu</span>
                                            @break
                                        @default
                                            <span class="badge bg-secondary">{{ ucfirst($potentialCustomer->offer_status) }}</span>
                                    @endswitch
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Teklif Tarihi:</label>
                                <p class="mb-2">
                                    @if($potentialCustomer->offer_date)
                                        {{ \Carbon\Carbon::parse($potentialCustomer->offer_date)->format('d.m.Y H:i') }}
                                    @else
                                        Belirtilmemiş
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Bizimle Çalışıyor mu?</label>
                                <p class="mb-2">
                                    @if($potentialCustomer->is_working_with_us)
                                        <span class="badge bg-success">Evet</span>
                                    @else
                                        <span class="badge bg-secondary">Hayır</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Sistem Bilgileri -->
                    <h5 class="mb-3 text-primary">Sistem Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Kayıt Tarihi:</label>
                                <p class="mb-2">{{ \Carbon\Carbon::parse($potentialCustomer->created_at)->format('d.m.Y H:i') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Son Güncelleme:</label>
                                <p class="mb-2">{{ \Carbon\Carbon::parse($potentialCustomer->updated_at)->format('d.m.Y H:i') }}</p>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="card-footer d-flex gap-2">
                    <a href="{{ route('potential-customers.edit', $potentialCustomer->id) }}" class="btn btn-primary flex-fill">Düzenle</a>
                    <a href="{{ route('customers.createFromPotential', $potentialCustomer->id) }}" class="btn btn-success flex-fill">Müşteriye Dönüştür</a>
                    <a href="{{ route('potential-customers.index') }}" class="btn btn-secondary flex-fill">Geri Dön</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-group {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 10px;
}

.info-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: block;
}

.info-group p {
    font-size: 1rem;
    margin: 0;
    color: #333;
}

.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
}
</style>
@endsection 
