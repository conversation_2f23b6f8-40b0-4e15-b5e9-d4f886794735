@extends('layouts.index')

@section('content')
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Potansi<PERSON>l Müşteri Listesi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Potansiyel Müşteri Listesi</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="{{ route('potential-customers.create') }}" class="btn btn-primary">Ye<PERSON> Potansiyel Müşteri</a>
                        </h3>

                        <div class="card-tools">
                            @if(session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif

                            <form method="GET" action="" class="d-flex">
                                <div class="input-group input-group-sm">
                                    <input type="text" name="q" class="form-control float-right" placeholder="telefon,isim,email,şirket" value="{{ request('q') }}">

                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button>
                                    </div>
                                </div>
                                <div class="input-group input-group-sm">
                                    <span class="align-self-center" style="margin-left:8px;">Sayfa başına kayıt:</span>
                                    <select name="perPage" class="form-select" style="width: auto; max-width: 120px;" onchange="this.form.submit()">
                                        <option value="5" {{ request('perPage') == 5 ? 'selected' : '' }}>5</option>
                                        <option value="10" {{ request('perPage', 10) == 10 ? 'selected' : '' }}>10</option>
                                        <option value="25" {{ request('perPage') == 25 ? 'selected' : '' }}>25</option>
                                        <option value="50" {{ request('perPage') == 50 ? 'selected' : '' }}>50</option>
                                        <option value="100" {{ request('perPage') == 100 ? 'selected' : '' }}>100</option>
                                    </select>
                                </div>
                                @if(request('q'))
                                    <a href="{{ route('potential-customers.index') }}" class="btn btn-outline-danger">Temizle</a>
                                @endif
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->

                    <div class="card-body table-responsive p-0" style="height: 100%;">

                        <table class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Firma Adı</th>
                                    <th>Yetkili İsim</th>
                                    <th>Yetkili Soyisim</th>
                                    <th>Şehir</th>
                                    <th>İlçe</th>
                                    <th>Teklif Durumu</th>
                                    <th>Teklif Tarihi</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($potentialCustomers as $customer)
                                    <tr>
                                        <td>{{ $customer->id }}</td>
                                        <td>{{ $customer->company_name }}</td>
                                        <td>
                                            @if($customer->authorizedPersons && $customer->authorizedPersons->count() > 0)
                                                {{ $customer->authorizedPersons->first()->name ?? '-' }}
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td>
                                            @if($customer->authorizedPersons && $customer->authorizedPersons->count() > 0)
                                                {{ $customer->authorizedPersons->first()->lastname ?? '-' }}
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td>{{ $customer->city }}</td>
                                        <td>{{ $customer->district }}</td>
                                        <td>{{ ucfirst($customer->offer_status) }}</td>
                                        <td>{{ $customer->offer_date }}</td>
                                        <td>
                                            <a href="{{ route('potential-customers.show', $customer->id) }}" class="btn btn-info btn-sm">Detay</a>
                                            <a href="{{ route('potential-customers.edit', $customer->id) }}" class="btn btn-warning btn-sm">Düzenle</a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer clearfix">
                        {{ $potentialCustomers->links('pagination::bootstrap-4') }}
                    </div>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection


