<?php $__env->startSection('content'); ?>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title text-center fw-bold py-2">Potansiyel Müşteri Düzenle</h3>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('potential-customers.update', $potentialCustomer->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <!-- Firma Bilgileri -->
                        <h5 class="mb-3 text-primary">Firma Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="company_name" class="form-label">Firma Adı <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" value="<?php echo e(old('company_name', $potentialCustomer->company_name)); ?>" required>
                                    <?php $__errorArgs = ['company_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Yetkili Kişi Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Yetkili Kişi Bilgileri</h5>
                        <div id="authorized-persons-container">
                            <?php if($potentialCustomer->authorizedPersons && $potentialCustomer->authorizedPersons->count() > 0): ?>
                                <?php $__currentLoopData = $potentialCustomer->authorizedPersons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $person): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="authorized-person-item border p-3 mb-3 rounded" data-index="<?php echo e($index); ?>">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">Yetkili Kişi #<?php echo e($index + 1); ?></h6>
                                            <button type="button" class="btn btn-sm btn-danger remove-authorized-person" <?php echo e($loop->first && $loop->count == 1 ? 'style=display:none;' : ''); ?>>
                                                <i class="fas fa-trash"></i> Kaldır
                                            </button>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili Ünvan</label>
                                                    <input type="text" class="form-control" name="authorized_persons[<?php echo e($index); ?>][title]" value="<?php echo e(old('authorized_persons.'.$index.'.title', $person->title)); ?>" placeholder="Örn: Genel Müdür, Satış Müdürü">
                                                    <?php $__errorArgs = ['authorized_persons.'.$index.'.title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili Telefon</label>
                                                    <input type="text" class="form-control inputmask" name="authorized_persons[<?php echo e($index); ?>][phone]" value="<?php echo e(old('authorized_persons.'.$index.'.phone', $person->phone)); ?>" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 5xx xxx xx xx">
                                                    <?php $__errorArgs = ['authorized_persons.'.$index.'.phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili İsim</label>
                                                    <input type="text" class="form-control" name="authorized_persons[<?php echo e($index); ?>][name]" value="<?php echo e(old('authorized_persons.'.$index.'.name', $person->name)); ?>" placeholder="Ad">
                                                    <?php $__errorArgs = ['authorized_persons.'.$index.'.name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili Soyisim</label>
                                                    <input type="text" class="form-control" name="authorized_persons[<?php echo e($index); ?>][lastname]" value="<?php echo e(old('authorized_persons.'.$index.'.lastname', $person->lastname)); ?>" placeholder="Soyad">
                                                    <?php $__errorArgs = ['authorized_persons.'.$index.'.lastname'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <div class="authorized-person-item border p-3 mb-3 rounded" data-index="0">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Yetkili Kişi #1</h6>
                                        <button type="button" class="btn btn-sm btn-danger remove-authorized-person" style="display: none;">
                                            <i class="fas fa-trash"></i> Kaldır
                                        </button>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Yetkili Ünvan</label>
                                                <input type="text" class="form-control" name="authorized_persons[0][title]" value="<?php echo e(old('authorized_persons.0.title')); ?>" placeholder="Örn: Genel Müdür, Satış Müdürü">
                                                <?php $__errorArgs = ['authorized_persons.0.title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Yetkili Telefon</label>
                                                <input type="text" class="form-control inputmask" name="authorized_persons[0][phone]" value="<?php echo e(old('authorized_persons.0.phone')); ?>" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 5xx xxx xx xx">
                                                <?php $__errorArgs = ['authorized_persons.0.phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Yetkili İsim</label>
                                                <input type="text" class="form-control" name="authorized_persons[0][name]" value="<?php echo e(old('authorized_persons.0.name')); ?>" placeholder="Ad">
                                                <?php $__errorArgs = ['authorized_persons.0.name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Yetkili Soyisim</label>
                                                <input type="text" class="form-control" name="authorized_persons[0][lastname]" value="<?php echo e(old('authorized_persons.0.lastname')); ?>" placeholder="Soyad">
                                                <?php $__errorArgs = ['authorized_persons.0.lastname'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="mb-3">
                            <button type="button" id="add-authorized-person" class="btn btn-sm btn-success">
                                <i class="fas fa-plus"></i> Yeni Yetkili Ekle
                            </button>
                        </div>

                        <!-- İletişim Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">İletişim Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="phone_1" class="form-label">Şirket Telefon 1</label>
                                    <input type="text" class="form-control inputmask" id="phone_1" name="phone_1" value="<?php echo e(old('phone_1', $potentialCustomer->phone_1)); ?>" data-inputmask="'mask': '+99 999 999 99 99'" inputmode="tel" pattern="\+[0-9 ]{13,16}" placeholder="+90 2xx xxx xx xx">
                                    <?php $__errorArgs = ['phone_1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="phone_2" class="form-label">Şirket Telefon 2</label>
                                    <input type="text" class="form-control inputmask" id="phone_2" name="phone_2" value="<?php echo e(old('phone_2', $potentialCustomer->phone_2)); ?>" data-inputmask="'mask': '+99 999 999 99 99'" inputmode="tel" pattern="\+[0-9 ]{13,16}" placeholder="+90 2xx xxx xx xx">
                                    <?php $__errorArgs = ['phone_2'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="phone_3" class="form-label">Şirket Telefon 3</label>
                                    <input type="text" class="form-control inputmask" id="phone_3" name="phone_3" value="<?php echo e(old('phone_3', $potentialCustomer->phone_3)); ?>" data-inputmask="'mask': '+99 999 999 99 99'" inputmode="tel" pattern="\+[0-9 ]{13,16}" placeholder="+90 2xx xxx xx xx">
                                    <?php $__errorArgs = ['phone_3'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Adres Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Adres Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="city" class="form-label">İl <span class="text-danger">*</span></label>
                                    <select class="form-control" id="city" name="city" required>
                                        <option value="">İl seçiniz</option>
                                    </select>
                                    <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="district" class="form-label">İlçe <span class="text-danger">*</span></label>
                                    <select class="form-control" id="district" name="district" required disabled>
                                        <option value="">İlçe seçiniz</option>
                                    </select>
                                    <?php $__errorArgs = ['district'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="address" class="form-label">Adres <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required placeholder="Detaylı adres bilgisi"><?php echo e(old('address', $potentialCustomer->address)); ?></textarea>
                                    <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Teklif ve Durum Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Teklif ve Durum Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="offer_status" class="form-label">Teklif Durumu <span class="text-danger">*</span></label>
                                    <select class="form-control" id="offer_status" name="offer_status" required>
                                        <option value="new" <?php echo e(old('offer_status', $potentialCustomer->offer_status) == 'new' ? 'selected' : ''); ?>>Yeni</option>
                                        <option value="pending" <?php echo e(old('offer_status', $potentialCustomer->offer_status) == 'pending' ? 'selected' : ''); ?>>Beklemede</option>
                                        <option value="accepted" <?php echo e(old('offer_status', $potentialCustomer->offer_status) == 'accepted' ? 'selected' : ''); ?>>Kabul Edildi</option>
                                        <option value="rejected" <?php echo e(old('offer_status', $potentialCustomer->offer_status) == 'rejected' ? 'selected' : ''); ?>>Reddedildi</option>
                                        <option value="customer" <?php echo e(old('offer_status', $potentialCustomer->offer_status) == 'customer' ? 'selected' : ''); ?>>Müşteri Oldu</option>
                                    </select>
                                    <?php $__errorArgs = ['offer_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="offer_date" class="form-label">Teklif Tarihi</label>
                                    <input type="date" class="form-control" id="offer_date" name="offer_date" value="<?php echo e(old('offer_date', $potentialCustomer->offer_date ? \Carbon\Carbon::parse($potentialCustomer->offer_date)->format('Y-m-d') : null)); ?>">
                                    <?php $__errorArgs = ['offer_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="is_working_with_us" class="form-label">Bizimle Çalışıyor mu? <span class="text-danger">*</span></label>
                                    <select class="form-control" id="is_working_with_us" name="is_working_with_us" required>
                                        <option value="0" <?php echo e(old('is_working_with_us', $potentialCustomer->is_working_with_us) == '0' ? 'selected' : ''); ?>>Hayır</option>
                                        <option value="1" <?php echo e(old('is_working_with_us', $potentialCustomer->is_working_with_us) == '1' ? 'selected' : ''); ?>>Evet</option>
                                    </select>
                                    <?php $__errorArgs = ['is_working_with_us'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer d-flex gap-2">
                            <a href="<?php echo e(route('customers.createFromPotential', $potentialCustomer->id)); ?>" class="btn btn-primary flex-fill">Müşteriye Dönüştür</a>
                            <button type="submit" class="btn btn-success flex-fill">Güncelle</button>
                            <a href="<?php echo e(route('potential-customers.index')); ?>" class="btn btn-secondary flex-fill">İptal</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="/assets/plugins/inputmask/jquery.inputmask.bundle.js"></script>
<script>
    let cityDistricts = {};
    $(document).ready(function(){
        const citySelect = $('#city');
        const districtSelect = $('#district');
        fetch('/assets/turkiye-il-ilce.json')
            .then(response => response.json())
            .then(data => {
                cityDistricts = data;
                Object.keys(cityDistricts).forEach(function(city) {
                    citySelect.append(`<option value="${city}">${city}</option>`);
                });
                // Eski değerleri doldur
                const oldCity = "<?php echo e(old('city', $potentialCustomer->city)); ?>";
                const oldDistrict = "<?php echo e(old('district', $potentialCustomer->district)); ?>";
                if (oldCity) {
                    citySelect.val(oldCity).trigger('change');
                    if (oldDistrict) {
                        setTimeout(function() {
                            districtSelect.val(oldDistrict);
                        }, 100);
                    }
                }
            });
        citySelect.on('change', function() {
            const selectedCity = $(this).val();
            districtSelect.empty().append('<option value="">İlçe seçiniz</option>');
            if (selectedCity && cityDistricts[selectedCity]) {
                cityDistricts[selectedCity].forEach(function(district) {
                    districtSelect.append(`<option value="${district}">${district}</option>`);
                });
                districtSelect.prop('disabled', false);
            } else {
                districtSelect.prop('disabled', true);
            }
        });
        $(".inputmask").inputmask({
            mask: "+99 999 999 99 99",
            showMaskOnHover: false,
            showMaskOnFocus: true,
            clearIncomplete: true,
            definitions: {
                '9': {
                    validator: "[0-9]",
                    cardinality: 1,
                    definitionSymbol: "9"
                }
            },
            onBeforePaste: function (pastedValue, opts) {
                return pastedValue.replace(/[^\d\+]/g, '');
            },
            onKeyDown: function(e, buffer, caretPos, opts) {
                var key = e.key;
                if (!/[0-9]/.test(key) && key.length === 1) {
                    e.preventDefault();
                }
            }
        });
    });

    // Yetkili kişi ekleme/çıkarma işlemleri
    let authorizedPersonIndex = <?php echo e($potentialCustomer->authorizedPersons ? $potentialCustomer->authorizedPersons->count() : 1); ?>;

    $('#add-authorized-person').on('click', function() {
        const container = $('#authorized-persons-container');
        const newItem = `
            <div class="authorized-person-item border p-3 mb-3 rounded" data-index="${authorizedPersonIndex}">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Yetkili Kişi #${authorizedPersonIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-authorized-person">
                        <i class="fas fa-trash"></i> Kaldır
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Ünvan</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][title]" placeholder="Örn: Genel Müdür, Satış Müdürü">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Telefon</label>
                            <input type="text" class="form-control inputmask" name="authorized_persons[${authorizedPersonIndex}][phone]" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 5xx xxx xx xx">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili İsim</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][name]" placeholder="Ad">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Soyisim</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][lastname]" placeholder="Soyad">
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.append(newItem);
        
        // Yeni eklenen telefon alanına inputmask uygula
        container.find('.inputmask').last().inputmask({
            mask: "+99 999 999 99 99",
            showMaskOnHover: false,
            showMaskOnFocus: true,
            clearIncomplete: true
        });
        
        authorizedPersonIndex++;
        updateRemoveButtons();
    });

    $(document).on('click', '.remove-authorized-person', function() {
        $(this).closest('.authorized-person-item').remove();
        updateRemoveButtons();
        updatePersonNumbers();
    });

    function updateRemoveButtons() {
        const items = $('.authorized-person-item');
        if (items.length > 1) {
            $('.remove-authorized-person').show();
        } else {
            $('.remove-authorized-person').hide();
        }
    }

    function updatePersonNumbers() {
        $('.authorized-person-item').each(function(index) {
            $(this).find('h6').text(`Yetkili Kişi #${index + 1}`);
        });
    }

    // Sayfa yüklendiğinde remove butonlarını kontrol et
    $(document).ready(function() {
        updateRemoveButtons();
    });
</script>
<?php $__env->stopPush(); ?> 




<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/potential_customers/edit.blade.php ENDPATH**/ ?>