<?php $__env->startSection('content'); ?>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title text-center fw-bold py-2">Potansiyel Müşteri Detayı</h3>
                </div>
                <div class="card-body">
                    
                    <!-- Firma Bilgileri -->
                    <h5 class="mb-3 text-primary">Firma Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Firma Adı:</label>
                                <p class="mb-2"><?php echo e($potentialCustomer->company_name ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Yet<PERSON><PERSON> Bilgileri -->
                    <h5 class="mb-3 text-primary">Yet<PERSON><PERSON></h5>
                    <?php if($potentialCustomer->authorizedPersons && $potentialCustomer->authorizedPersons->count() > 0): ?>
                        <?php $__currentLoopData = $potentialCustomer->authorizedPersons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $person): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-secondary mb-3">Yetkili Kişi #<?php echo e($index + 1); ?></h6>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Ünvan:</label>
                                        <p class="mb-2"><?php echo e($person->title ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Telefon:</label>
                                        <p class="mb-2"><?php echo e($person->phone ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili İsim:</label>
                                        <p class="mb-2"><?php echo e($person->name ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Soyisim:</label>
                                        <p class="mb-2"><?php echo e($person->lastname ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php if(!$loop->last): ?>
                                <hr class="my-4">
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="info-group">
                                    <p class="mb-2 text-muted">Yetkili kişi bilgisi bulunmamaktadır.</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- İletişim Bilgileri -->
                    <h5 class="mb-3 text-primary">İletişim Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şirket Telefon 1:</label>
                                <p class="mb-2"><?php echo e($potentialCustomer->phone_1 ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şirket Telefon 2:</label>
                                <p class="mb-2"><?php echo e($potentialCustomer->phone_2 ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şirket Telefon 3:</label>
                                <p class="mb-2"><?php echo e($potentialCustomer->phone_3 ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Adres Bilgileri -->
                    <h5 class="mb-3 text-primary">Adres Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İl:</label>
                                <p class="mb-2"><?php echo e($potentialCustomer->city ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İlçe:</label>
                                <p class="mb-2"><?php echo e($potentialCustomer->district ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Adres:</label>
                                <p class="mb-2"><?php echo e($potentialCustomer->address ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Teklif ve Durum Bilgileri -->
                    <h5 class="mb-3 text-primary">Teklif ve Durum Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Teklif Durumu:</label>
                                <p class="mb-2">
                                    <?php switch($potentialCustomer->offer_status):
                                        case ('new'): ?>
                                            <span class="badge bg-primary">Yeni</span>
                                            <?php break; ?>
                                        <?php case ('pending'): ?>
                                            <span class="badge bg-warning">Beklemede</span>
                                            <?php break; ?>
                                        <?php case ('accepted'): ?>
                                            <span class="badge bg-success">Kabul Edildi</span>
                                            <?php break; ?>
                                        <?php case ('rejected'): ?>
                                            <span class="badge bg-danger">Reddedildi</span>
                                            <?php break; ?>
                                        <?php case ('customer'): ?>
                                            <span class="badge bg-info">Müşteri Oldu</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge bg-secondary"><?php echo e(ucfirst($potentialCustomer->offer_status)); ?></span>
                                    <?php endswitch; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Teklif Tarihi:</label>
                                <p class="mb-2">
                                    <?php if($potentialCustomer->offer_date): ?>
                                        <?php echo e(\Carbon\Carbon::parse($potentialCustomer->offer_date)->format('d.m.Y H:i')); ?>

                                    <?php else: ?>
                                        Belirtilmemiş
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Bizimle Çalışıyor mu?</label>
                                <p class="mb-2">
                                    <?php if($potentialCustomer->is_working_with_us): ?>
                                        <span class="badge bg-success">Evet</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Hayır</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Sistem Bilgileri -->
                    <h5 class="mb-3 text-primary">Sistem Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Kayıt Tarihi:</label>
                                <p class="mb-2"><?php echo e(\Carbon\Carbon::parse($potentialCustomer->created_at)->format('d.m.Y H:i')); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Son Güncelleme:</label>
                                <p class="mb-2"><?php echo e(\Carbon\Carbon::parse($potentialCustomer->updated_at)->format('d.m.Y H:i')); ?></p>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="card-footer d-flex gap-2">
                    <a href="<?php echo e(route('potential-customers.edit', $potentialCustomer->id)); ?>" class="btn btn-primary flex-fill">Düzenle</a>
                    <a href="<?php echo e(route('customers.createFromPotential', $potentialCustomer->id)); ?>" class="btn btn-success flex-fill">Müşteriye Dönüştür</a>
                    <a href="<?php echo e(route('potential-customers.index')); ?>" class="btn btn-secondary flex-fill">Geri Dön</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-group {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 10px;
}

.info-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: block;
}

.info-group p {
    font-size: 1rem;
    margin: 0;
    color: #333;
}

.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
}
</style>
<?php $__env->stopSection(); ?> 

<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/potential_customers/show.blade.php ENDPATH**/ ?>